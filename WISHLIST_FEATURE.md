# Dynamic Wishlist Status Feature

## Overview

The MENG E-Commerce API now includes a dynamic wishlist status feature that automatically adds an `isWishlisted` field to all product responses based on the logged-in user's wishlist.

## How It Works

### For Logged-in Users
- When a user is authenticated, all product responses include an `isWishlisted` boolean field
- `isWishlisted: true` - Product is in the user's wishlist
- `isWishlisted: false` - Product is not in the user's wishlist

### For Anonymous Users
- When no user is logged in, all products show `isWishlisted: false`

## Affected Endpoints

This feature automatically applies to all endpoints that return product data:

### Product Endpoints
- `GET /api/v1/products` - All products with wishlist status
- `GET /api/v1/products/:id` - Single product with wishlist status
- `GET /api/v1/products/:id/similar` - Similar products with wishlist status

### Search Endpoints
- `GET /api/v1/llm-search?q=query` - AI search results with wishlist status

### Wishlist Endpoints
- `GET /api/v1/wishlist` - User's wishlist (all items show `isWishlisted: true`)
- `POST /api/v1/wishlist/:productId` - Add to wishlist
- `DELETE /api/v1/wishlist/:productId` - Remove from wishlist

## Example Responses

### Product List Response (Logged-in User)
```json
{
  "status": "success",
  "results": 2,
  "data": [
    {
      "_id": "64a1b2c3d4e5f6789012345a",
      "name": "Gaming Laptop",
      "price": 1299.99,
      "imageCover": "laptop-cover.jpg",
      "isWishlisted": true
    },
    {
      "_id": "64a1b2c3d4e5f6789012345b",
      "name": "Wireless Mouse",
      "price": 29.99,
      "imageCover": "mouse-cover.jpg",
      "isWishlisted": false
    }
  ]
}
```

### Product List Response (Anonymous User)
```json
{
  "status": "success",
  "results": 2,
  "data": [
    {
      "_id": "64a1b2c3d4e5f6789012345a",
      "name": "Gaming Laptop",
      "price": 1299.99,
      "imageCover": "laptop-cover.jpg",
      "isWishlisted": false
    },
    {
      "_id": "64a1b2c3d4e5f6789012345b",
      "name": "Wireless Mouse",
      "price": 29.99,
      "imageCover": "mouse-cover.jpg",
      "isWishlisted": false
    }
  ]
}
```

### Single Product Response
```json
{
  "status": "success",
  "data": {
    "_id": "64a1b2c3d4e5f6789012345a",
    "name": "Gaming Laptop",
    "description": "High-performance gaming laptop...",
    "price": 1299.99,
    "imageCover": "laptop-cover.jpg",
    "images": ["laptop-1.jpg", "laptop-2.jpg"],
    "category": "Electronics",
    "brand": "TechBrand",
    "ratingsAverage": 4.5,
    "ratingsQuantity": 128,
    "isWishlisted": true,
    "similarProducts": [
      {
        "product": {
          "_id": "64a1b2c3d4e5f6789012345c",
          "name": "Gaming Desktop",
          "price": 1599.99,
          "isWishlisted": false
        },
        "similarityScore": 0.85
      }
    ]
  }
}
```

### AI Search Response
```json
{
  "originalQuery": "gaming laptop under $1500",
  "searchMethod": "AI-Enhanced Search",
  "resultsCount": 3,
  "results": [
    {
      "_id": "64a1b2c3d4e5f6789012345a",
      "name": "Gaming Laptop Pro",
      "price": 1299.99,
      "isWishlisted": true
    },
    {
      "_id": "64a1b2c3d4e5f6789012345d",
      "name": "Budget Gaming Laptop",
      "price": 899.99,
      "isWishlisted": false
    }
  ]
}
```

## Implementation Details

### Backend Implementation
The feature is implemented using a utility function `addWishlistStatus()` in the wishlist service that:

1. Checks if a user is logged in
2. Retrieves the user's wishlist from the database
3. Compares product IDs with the wishlist
4. Adds the `isWishlisted` field to each product

### Performance Considerations
- Single database query per request to fetch user's wishlist
- Efficient ID comparison using string matching
- Works with both single products and arrays of products
- Minimal performance impact on API responses

### Error Handling
- Gracefully handles cases where user is not found
- Defaults to `isWishlisted: false` for any errors
- Does not break existing functionality if wishlist service fails

## Frontend Integration

### JavaScript Example
```javascript
// Fetch products with wishlist status
fetch('/api/v1/products', {
  headers: {
    'Authorization': `Bearer ${userToken}` // Include user token
  }
})
.then(response => response.json())
.then(data => {
  data.data.forEach(product => {
    // Use the isWishlisted field to update UI
    if (product.isWishlisted) {
      showWishlistIcon(product._id, 'filled');
    } else {
      showWishlistIcon(product._id, 'empty');
    }
  });
});

// Toggle wishlist status
function toggleWishlist(productId, isCurrentlyWishlisted) {
  const method = isCurrentlyWishlisted ? 'DELETE' : 'POST';
  const url = `/api/v1/wishlist/${productId}`;
  
  fetch(url, {
    method: method,
    headers: {
      'Authorization': `Bearer ${userToken}`
    }
  })
  .then(response => response.json())
  .then(data => {
    // Update UI based on new status
    const newStatus = !isCurrentlyWishlisted;
    showWishlistIcon(productId, newStatus ? 'filled' : 'empty');
  });
}
```

### React Example
```jsx
function ProductCard({ product }) {
  const [isWishlisted, setIsWishlisted] = useState(product.isWishlisted);
  
  const toggleWishlist = async () => {
    try {
      const method = isWishlisted ? 'DELETE' : 'POST';
      const response = await fetch(`/api/v1/wishlist/${product._id}`, {
        method,
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        setIsWishlisted(!isWishlisted);
      }
    } catch (error) {
      console.error('Error toggling wishlist:', error);
    }
  };
  
  return (
    <div className="product-card">
      <h3>{product.name}</h3>
      <p>${product.price}</p>
      <button 
        onClick={toggleWishlist}
        className={`wishlist-btn ${isWishlisted ? 'active' : ''}`}
      >
        {isWishlisted ? '❤️' : '🤍'}
      </button>
    </div>
  );
}
```

## Testing

The feature includes comprehensive tests in `test/wishlistTest.js` that verify:
- Correct wishlist status for logged-in users
- Default false status for anonymous users
- Handling of both single products and product arrays
- Error handling scenarios

Run tests with:
```bash
node test/wishlistTest.js
```

## Benefits

1. **Real-time Wishlist Status**: Users see current wishlist status without additional API calls
2. **Improved UX**: Consistent wishlist indicators across all product displays
3. **Reduced API Calls**: No need for separate wishlist status checks
4. **Automatic Updates**: Status updates automatically when wishlist is modified
5. **Backward Compatible**: Existing API consumers continue to work normally
