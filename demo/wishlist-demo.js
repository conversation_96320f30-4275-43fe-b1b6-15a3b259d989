/**
 * Wishlist Feature Demo
 * 
 * This script demonstrates how the dynamic wishlist status feature works
 * in the MENG E-Commerce API.
 */

const express = require('express');
const app = express();

// Mock data to simulate the feature
const mockProducts = [
    {
        _id: '64a1b2c3d4e5f6789012345a',
        name: 'Gaming Laptop Pro',
        price: 1299.99,
        imageCover: 'laptop-pro.jpg',
        description: 'High-performance gaming laptop with RTX graphics'
    },
    {
        _id: '64a1b2c3d4e5f6789012345b',
        name: 'Wireless Gaming Mouse',
        price: 79.99,
        imageCover: 'gaming-mouse.jpg',
        description: 'Precision wireless gaming mouse with RGB lighting'
    },
    {
        _id: '64a1b2c3d4e5f6789012345c',
        name: 'Mechanical Keyboard',
        price: 149.99,
        imageCover: 'keyboard.jpg',
        description: 'RGB mechanical keyboard with blue switches'
    }
];

const mockUsers = {
    'user123': {
        _id: 'user123',
        name: '<PERSON>',
        wishlist: ['64a1b2c3d4e5f6789012345a', '64a1b2c3d4e5f6789012345c'] // Has laptop and keyboard in wishlist
    }
};

// Middleware to parse JSON
app.use(express.json());

// Middleware to simulate authentication
app.use((req, res, next) => {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        // In real app, you would verify the JWT token
        // For demo, we'll just check if it's a known user
        if (mockUsers[token]) {
            req.user = mockUsers[token];
        }
    }
    next();
});

// Utility function to add wishlist status (simulating the real implementation)
function addWishlistStatus(products, user) {
    const userWishlist = user ? user.wishlist : [];
    
    if (Array.isArray(products)) {
        return products.map(product => ({
            ...product,
            isWishlisted: userWishlist.includes(product._id)
        }));
    } else {
        return {
            ...products,
            isWishlisted: userWishlist.includes(products._id)
        };
    }
}

// Demo endpoints

// Get all products
app.get('/api/v1/products', (req, res) => {
    const productsWithWishlistStatus = addWishlistStatus(mockProducts, req.user);
    
    res.json({
        status: 'success',
        results: productsWithWishlistStatus.length,
        data: productsWithWishlistStatus
    });
});

// Get single product
app.get('/api/v1/products/:id', (req, res) => {
    const product = mockProducts.find(p => p._id === req.params.id);
    
    if (!product) {
        return res.status(404).json({
            status: 'error',
            message: 'Product not found'
        });
    }
    
    const productWithWishlistStatus = addWishlistStatus(product, req.user);
    
    res.json({
        status: 'success',
        data: productWithWishlistStatus
    });
});

// Add to wishlist
app.post('/api/v1/wishlist/:productId', (req, res) => {
    if (!req.user) {
        return res.status(401).json({
            status: 'error',
            message: 'Authentication required'
        });
    }
    
    const productId = req.params.productId;
    const product = mockProducts.find(p => p._id === productId);
    
    if (!product) {
        return res.status(404).json({
            status: 'error',
            message: 'Product not found'
        });
    }
    
    if (!req.user.wishlist.includes(productId)) {
        req.user.wishlist.push(productId);
    }
    
    res.json({
        status: 'success',
        message: 'Product added to wishlist',
        data: req.user.wishlist
    });
});

// Remove from wishlist
app.delete('/api/v1/wishlist/:productId', (req, res) => {
    if (!req.user) {
        return res.status(401).json({
            status: 'error',
            message: 'Authentication required'
        });
    }
    
    const productId = req.params.productId;
    const index = req.user.wishlist.indexOf(productId);
    
    if (index > -1) {
        req.user.wishlist.splice(index, 1);
    }
    
    res.json({
        status: 'success',
        message: 'Product removed from wishlist',
        data: req.user.wishlist
    });
});

// Get user's wishlist
app.get('/api/v1/wishlist', (req, res) => {
    if (!req.user) {
        return res.status(401).json({
            status: 'error',
            message: 'Authentication required'
        });
    }
    
    const wishlistProducts = mockProducts.filter(product => 
        req.user.wishlist.includes(product._id)
    );
    
    // All products in wishlist should have isWishlisted: true
    const wishlistWithStatus = wishlistProducts.map(product => ({
        ...product,
        isWishlisted: true
    }));
    
    res.json({
        status: 'success',
        results: wishlistWithStatus.length,
        data: wishlistWithStatus
    });
});

// Demo instructions endpoint
app.get('/', (req, res) => {
    res.json({
        message: 'MENG E-Commerce API - Wishlist Feature Demo',
        instructions: {
            'Get products (anonymous)': 'GET /api/v1/products',
            'Get products (logged in)': 'GET /api/v1/products with Authorization: Bearer user123',
            'Get single product': 'GET /api/v1/products/64a1b2c3d4e5f6789012345a',
            'Add to wishlist': 'POST /api/v1/wishlist/64a1b2c3d4e5f6789012345b with Authorization: Bearer user123',
            'Remove from wishlist': 'DELETE /api/v1/wishlist/64a1b2c3d4e5f6789012345a with Authorization: Bearer user123',
            'Get wishlist': 'GET /api/v1/wishlist with Authorization: Bearer user123'
        },
        examples: {
            'Login as user': 'Add header: Authorization: Bearer user123',
            'Current user wishlist': ['64a1b2c3d4e5f6789012345a', '64a1b2c3d4e5f6789012345c']
        }
    });
});

const PORT = process.env.PORT || 3001;

if (require.main === module) {
    app.listen(PORT, () => {
        console.log(`🚀 Wishlist Demo Server running on port ${PORT}`);
        console.log(`📖 Visit http://localhost:${PORT} for instructions`);
        console.log('\n📝 Example requests:');
        console.log(`   Anonymous: curl http://localhost:${PORT}/api/v1/products`);
        console.log(`   Logged in: curl -H "Authorization: Bearer user123" http://localhost:${PORT}/api/v1/products`);
        console.log(`   Add to wishlist: curl -X POST -H "Authorization: Bearer user123" http://localhost:${PORT}/api/v1/wishlist/64a1b2c3d4e5f6789012345b`);
    });
}

module.exports = app;
