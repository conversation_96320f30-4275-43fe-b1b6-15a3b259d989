# Graduation Project API

[![Node.js](https://img.shields.io/badge/Node.js-v18.x-green.svg)](https://nodejs.org/)
[![Express](https://img.shields.io/badge/Express-v4.21.2-blue.svg)](https://expressjs.com/)
[![MongoDB](https://img.shields.io/badge/MongoDB-v5.9.2-green.svg)](https://www.mongodb.com/)
[![License](https://img.shields.io/badge/License-ISC-blue.svg)](LICENSE)

A robust RESTful API built with Express.js and MongoDB for managing data with features including authentication, file uploads, data validation, product similarity recommendations, and AI-powered search.

## Technologies Used

- **Express.js**: Fast, unopinionated web framework for Node.js
- **MongoDB/Mongoose**: NoSQL database and elegant MongoDB object modeling
- **Multer**: Middleware for handling multipart/form-data for file uploads
- **Sharp**: High-performance image processing library
- **Express Validator**: Middleware for data validation
- **UUID**: For generating unique identifiers
- **Slugify**: For creating URL-friendly slugs
- **Natural**: NLP library for text processing and similarity calculations
- **JWT**: JSON Web Tokens for authentication
- **Bcrypt**: Password hashing
- **Passport**: Authentication middleware for OAuth strategies
- **Google Generative AI**: Integration with Gemini API for AI-powered search

## Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/graduation.git

# Navigate to the project directory
cd graduation

# Install dependencies
npm install

# Set up environment variables
cp config.env.example config.env
# Edit config.env with your configuration

# Start development server
npm run start:dev

# Start production server
npm run start:prod
```

## API Endpoints

### Category Endpoints
- `GET /api/v1/categories` - Get all categories
- `POST /api/v1/categories` - Create a new category
- `GET /api/v1/categories/:id` - Get a specific category
- `PUT /api/v1/categories/:id` - Update a specific category
- `DELETE /api/v1/categories/:id` - Delete a specific category

### Subcategory Endpoints
- `GET /api/v1/subcategories` - Get all subcategories
- `POST /api/v1/subcategories` - Create a new subcategory
- `GET /api/v1/subcategories/:id` - Get a specific subcategory
- `PUT /api/v1/subcategories/:id` - Update a specific subcategory
- `DELETE /api/v1/subcategories/:id` - Delete a specific subcategory
- `GET /api/v1/categories/:categoryId/subcategory` - Get subcategories for a specific category

### Brand Endpoints
- `GET /api/v1/brand` - Get all brands
- `POST /api/v1/brand` - Create a new brand
- `GET /api/v1/brand/:id` - Get a specific brand
- `PUT /api/v1/brand/:id` - Update a specific brand
- `DELETE /api/v1/brand/:id` - Delete a specific brand

### Product Endpoints
- `GET /api/v1/products` - Get all products
- `POST /api/v1/products` - Create a new product
- `GET /api/v1/products/:id` - Get a specific product
- `PUT /api/v1/products/:id` - Update a specific product
- `DELETE /api/v1/products/:id` - Delete a specific product
- `GET /api/v1/products/:productId/similar` - Get similar products based on content similarity

### Review Endpoints
- `GET /api/v1/reviews` - Get all reviews
- `POST /api/v1/reviews` - Create a new review
- `GET /api/v1/reviews/:id` - Get a specific review
- `PUT /api/v1/reviews/:id` - Update a specific review
- `DELETE /api/v1/reviews/:id` - Delete a specific review

### Authentication Endpoints
- `POST /api/v1/auth/signup` - Register a new user
- `POST /api/v1/auth/login` - Login a user
- `GET /api/v1/auth/logout` - Logout a user
- `POST /api/v1/auth/forgotPassword` - Request password reset
- `POST /api/v1/auth/verifyResetCode` - Verify password reset code
- `PUT /api/v1/auth/resetPassword` - Reset password with code

### OAuth Endpoints
- `GET /api/v1/auth/google` - Authenticate with Google
- `GET /api/v1/auth/google/callback` - Google OAuth callback
- `GET /api/v1/auth/facebook` - Authenticate with Facebook
- `GET /api/v1/auth/facebook/callback` - Facebook OAuth callback

### User Endpoints
- `GET /api/v1/users/me` - Get current user profile
- `PUT /api/v1/users/updateMe` - Update current user profile
- `PUT /api/v1/users/changeMyPassword` - Change current user password
- `DELETE /api/v1/users/deleteMe` - Delete current user account

### Wishlist Endpoints
- `GET /api/v1/wishlist` - Get user's wishlist
- `POST /api/v1/wishlist` - Add product to wishlist
- `DELETE /api/v1/wishlist/:productId` - Remove product from wishlist

### Address Endpoints
- `GET /api/v1/address` - Get user's addresses
- `POST /api/v1/address` - Add a new address
- `PUT /api/v1/address/:addressId` - Update an address
- `DELETE /api/v1/address/:addressId` - Delete an address

### Phone OTP Endpoints
- `POST /api/auth/phone/send-otp` - Send OTP to phone
- `POST /api/auth/phone/verify-otp` - Verify phone OTP

### Coupon Endpoints
- `GET /api/v1/coupon` - Get all coupons
- `POST /api/v1/coupon` - Create a new coupon
- `GET /api/v1/coupon/:id` - Get a specific coupon
- `PUT /api/v1/coupon/:id` - Update a specific coupon
- `DELETE /api/v1/coupon/:id` - Delete a specific coupon

### Cart Endpoints
- `GET /api/v1/cart` - Get user's cart
- `POST /api/v1/cart` - Add product to cart
- `PUT /api/v1/cart/:itemId` - Update cart item
- `DELETE /api/v1/cart/:itemId` - Remove item from cart
- `DELETE /api/v1/cart` - Clear cart

### Order Endpoints
- `GET /api/v1/orders` - Get user's orders
- `POST /api/v1/orders` - Create a new order
- `GET /api/v1/orders/:id` - Get a specific order
- `PUT /api/v1/orders/:id/pay` - Update order to paid
- `PUT /api/v1/orders/:id/deliver` - Update order to delivered

### AI-Powered Search Endpoint
- `GET /api/v1/llm-search?q=your search query` - Search products using AI language understanding

## Environment Variables

The application uses the following environment variables:

```
PORT=8000
NODE_ENV=development
HOST=0.0.0.0

# Database
DB_USERNAME=your_username
DB_PASSWORD=your_password
DB_URI=your_mongodb_connection_string
DB_NAME=your_database_name

# JWT
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=90d
JWT_COOKIE_EXPIRES_IN=90

# Email
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_email_password

# OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret

# Similarity Calculation
SIMILARITY_THRESHOLD=0.1
BATCH_SIZE=100

# AI Search
GEMINI_API_KEY=your_gemini_api_key
```

## Project Structure

```
├── config/             # Configuration files
├── controllers/        # Request handlers
├── middlewares/        # Custom middleware functions
├── models/             # Mongoose models
├── routes/             # API routes
│   ├── categoryRoute.js
│   ├── subCategoryRoute.js
│   ├── brandRoute.js
│   ├── productRoute.js
│   ├── reviewRoute.js
│   ├── authRoute.js
│   ├── userRoute.js
│   ├── wishlistRoute.js
│   ├── addressRoute.js
│   ├── phoneOtpRoute.js
│   ├── couponRoute.js
│   ├── cartRoute.js
│   ├── orderRoute.js
│   └── llmSearchRoute.js
├── services/           # Business logic
│   ├── auth/           # Authentication services
│   ├── handlersFactory.js  # Generic CRUD operations
│   ├── productService.js   # Product-related services
│   └── llmSearchService.js # AI-powered search service
├── utils/              # Utility functions
│   ├── validators/     # Request validation
│   └── apiFeatures.js  # Query building and pagination
├── uploads/            # Uploaded files
├── server.js           # Application entry point
├── similarity-calculator.js  # Product similarity calculation script
└── package.json        # Project dependencies
```

## Features

- **RESTful API Design**: Follows REST principles for intuitive API design
- **MongoDB Integration**: Efficient data storage and retrieval
- **Image Processing**: Resize and optimize uploaded images
- **Data Validation**: Comprehensive request validation
- **Error Handling**: Consistent error responses
- **Authentication**: JWT-based authentication with refresh tokens
- **Authorization**: Role-based access control
- **Social Login**: OAuth integration with Google and Facebook
- **Password Management**: Secure password reset flow
- **Product Recommendations**: Content-based similarity using TF-IDF and cosine similarity
- **Environment Configuration**: Different settings for development and production
- **AI-Powered Search**: Natural language understanding for product search

## Product Similarity Engine

The project includes a product recommendation system based on content similarity:

- **TF-IDF Vectorization**: Converts product text (name, description) into numerical vectors
- **Cosine Similarity**: Measures similarity between product vectors
- **Batch Processing**: Efficiently processes large product catalogs
- **Pre-computation**: Calculates similarities offline and stores results for fast retrieval
- **Progress Visualization**: Visual feedback during similarity calculation

To run the similarity calculation:

```bash
# Run the similarity calculator
node similarity-calculator.js
```

This will:
1. Connect to your MongoDB database
2. Fetch all products
3. Calculate similarity scores between products using TF-IDF and cosine similarity
4. Store the results in the ProductSimilarity collection
5. Display progress with visual indicators

## AI-Powered Search Implementation

The project includes an advanced search feature powered by Google's Gemini AI:

### Key Features

- **Natural Language Understanding**: Interprets user queries in any language
- **Semantic Search**: Understands the meaning behind search terms, not just keywords
- **Multi-parameter Extraction**: Identifies product attributes, categories, price ranges, and more
- **Fallback Mechanism**: Gracefully degrades to keyword search if AI processing fails

### Implementation Details

- **Gemini API Integration**: Uses Google's Generative AI for query understanding
- **Prompt Engineering**: Carefully crafted prompts to extract structured data from natural language
- **MongoDB Query Construction**: Dynamically builds database queries based on AI interpretation
- **Error Handling**: Robust error handling with graceful fallback to basic search

### Usage

```
GET /api/v1/llm-search?q=your search query
```

Example queries:
- "red gaming laptop under $1500 with RTX graphics"
- "summer dresses for women"
- "safety shoes size 42 for less than 500 pounds"

### Response Format

```json
{
  "originalQuery": "user's original query",
  "searchMethod": "AI-Enhanced Search",
  "llmInterpretation": {
    "keywords": ["extracted", "keywords"],
    "categories": ["identified", "categories"],
    "minPrice": 100,
    "maxPrice": 500
  },
  "mongoQueryUsed": {
    "$or": [
      { "name": { "$in": [/keyword1/i, /keyword2/i] } },
      { "description": { "$in": [/keyword1/i, /keyword2/i] } }
    ],
    "price": { "$gte": 100, "$lte": 500 }
  },
  "resultsCount": 5,
  "results": [
    // Array of product objects
  ]
}
```

## Key Notes and Observations for the LLM Search Implementation

### Core Goal
The primary objective is to use the Gemini Large Language Model (LLM) to interpret a user's natural language search query (in any language) for an e-commerce context. The LLM's interpretation is then used to construct a MongoDB query to find relevant products. A basic keyword-based search serves as a fallback if the AI processing fails.

### Gemini API Integration
- **Initialization**: The GoogleGenerativeAI client and the specific model are initialized once when the module loads, provided the GEMINI_API_KEY is present in the environment variables.
- **API Key Check**: A crucial check for GEMINI_API_KEY at startup prevents the application from running with a non-functional AI search if the key is missing.
- **Model Name Flexibility**: The code tries multiple model names to find one that works with the current API version.
- **Prompt Engineering**: The prompt is designed to instruct the LLM to act as an e-commerce query parser and demand a strict JSON-only output.

### LLM Output Processing
- **JSON Cleaning**: Logic is dedicated to cleaning the LLM's raw text output to isolate a valid JSON string.
- **JSON Parsing**: JSON.parse() is used on the cleaned text. If this fails, the system gracefully falls back to the basic search.

### MongoDB Query Construction
- **Dynamic Query Building**: The mongoQuery object is built dynamically based on the parameters extracted from the LLM's JSON output.
- **Keyword Search**: Uses $or with case-insensitive regular expressions for keywords across name, description, and tags.
- **Categorical Filters**: Uses $in with RegExp for categories and brand.
- **Price Range**: Uses $gte for minPrice and $lte for maxPrice.

### Fallback Search Mechanism
- **Robustness**: If any part of the AI processing pipeline fails, the system falls back to basic search.
- **Basic Keyword Extraction**: The fallback splits the user query into words and uses them for a regex search.
- **Simple Price Extraction**: The fallback attempts to extract numbers from the query for price filtering.
- **Clear Indication**: The response JSON clearly indicates when the fallback search was used and the reason.

### Potential Areas for Further Improvement
- **LLM Output Validation**: Use a schema validation library for more robust parsing.
- **More Sophisticated Fallback**: Implement more advanced NLP techniques for the fallback search.
- **$text Search**: Consider creating a $text index for more efficient keyword searching.
- **Advanced Prompting**: Explore more advanced prompting techniques for complex queries.
- **Performance Monitoring**: Monitor latency introduced by the LLM call.
- **Cost Management**: Implement logging and monitoring for API usage.
- **User Feedback Loop**: Collect feedback on search results to fine-tune prompts over time.

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the ISC License.
# Natural: AI Product Similarity for E-commerce

A powerful Node.js application that calculates product similarities using Natural Language Processing (NLP) techniques to enhance e-commerce recommendation systems.

## Overview

This application analyzes product names and descriptions to identify similar products using Term Frequency-Inverse Document Frequency (TF-IDF) and cosine similarity algorithms. The results are stored in MongoDB for quick access by e-commerce platforms to power "You might also like" or "Similar products" features.

## Features

- **TF-IDF Vectorization**: Converts product text into numerical vectors
- **Cosine Similarity Calculation**: Measures similarity between products
- **Batch Processing**: Efficiently handles large product catalogs
- **Progress Visualization**: Real-time terminal UI showing calculation progress
- **MongoDB Integration**: Stores similarity results for quick retrieval
- **Configurable Parameters**: Adjustable similarity threshold and batch size

## Technical Implementation

### Mathematical Foundation

#### TF-IDF (Term Frequency-Inverse Document Frequency)

TF-IDF measures the importance of a word in a document relative to a collection of documents:

1. **Term Frequency (TF)**: How often a word appears in a document
   ```
   TF(t) = (Number of times term t appears in document) / (Total number of terms in document)
   ```

2. **Inverse Document Frequency (IDF)**: How unique or rare a word is across all documents
   ```
   IDF(t) = log_e(Total number of documents / Number of documents containing term t)
   ```

3. **TF-IDF Score**: The product of TF and IDF
   ```
   TF-IDF(t) = TF(t) × IDF(t)
   ```

#### Cosine Similarity

Cosine similarity measures the cosine of the angle between two non-zero vectors, determining how similar they are regardless of their magnitude:

```
cosine_similarity(A, B) = (A · B) / (||A|| × ||B||)
```

Where:
- A · B is the dot product of vectors A and B
- ||A|| and ||B|| are the magnitudes (Euclidean norms) of vectors A and B

In our implementation:
```javascript
function cosineSimilarity(vecA, vecB) {
    let dotProduct = 0;
    let magnitudeA = 0;
    let magnitudeB = 0;
    const allTerms = new Set([...Object.keys(vecA), ...Object.keys(vecB)]);
    
    allTerms.forEach(term => {
        const valA = vecA[term] || 0;
        const valB = vecB[term] || 0;
        dotProduct += valA * valB;
        magnitudeA += valA * valA;
        magnitudeB += valB * valB;
    });
    
    magnitudeA = Math.sqrt(magnitudeA);
    magnitudeB = Math.sqrt(magnitudeB);
    
    if (magnitudeA === 0 || magnitudeB === 0) return 0;
    return dotProduct / (magnitudeA * magnitudeB);
}
```

### Core Functions

#### 1. TF-IDF Corpus Building
```javascript
// Build TF-IDF Corpus
const tfidf = new TfIdf();
allProducts.forEach((product, index) => {
  tfidf.addDocument(`${product.name} ${product.description || ''}`);
});
```
This function creates a TF-IDF model from all product descriptions, establishing the foundation for similarity calculations.

#### 2. Vector Calculation
```javascript
// Pre-calculate all vectors
const productVectors = [];
for (let idx = 0; idx < allProducts.length; idx++) {
  const product = allProducts[idx];
  const vector = {};
  const text = `${product.name} ${product.description || ''}`.toLowerCase();
  const terms = tokenizer.tokenize(text);
  
  terms.forEach(term => {
    vector[term] = tfidf.tfidf(term, idx);
  });
  
  productVectors.push({ product, vector });
}
```
This function converts each product's text into a numerical vector where each dimension represents a term's TF-IDF score.

#### 3. Similarity Calculation
```javascript
for (let j = 0; j < batch.length; j++) {
  const { product: productA, vector: vectorA } = batch[j];
  const currentSimilarities = [];
  
  for (const { product: productB, vector: vectorB } of productVectors) {
    if (productA._id.toString() === productB._id.toString()) continue;
    
    const similarity = cosineSimilarity(vectorA, vectorB);
    
    if (similarity >= similarityThreshold) {
      currentSimilarities.push({
        similarProductId: productB._id,
        similarityScore: similarity
      });
    }
  }
  
  // Store similarities...
}
```
This function calculates the cosine similarity between each pair of products and stores the results if they exceed the threshold.

### Terminal UI Design

The application features an intuitive terminal UI with:

1. **Progress Bars**: Visual indicators for each processing stage
   ```javascript
   const corpusBar = new cliProgress.SingleBar({
     format: 'Building Corpus |' + colors.cyan('{bar}') + '| {percentage}% | {value}/{total} products',
     barCompleteChar: '\u2588',
     barIncompleteChar: '\u2591',
     hideCursor: true
   });
   ```

2. **Color-Coded Messages**: Different colors for different types of information
   - Cyan: Process titles and summaries
   - Yellow: Progress updates and ongoing operations
   - Green: Success messages and completions
   - Red: Error messages

3. **Emoji Icons**: Visual cues for different stages
   - 🚀 Starting processes
   - 📊 Database operations
   - 🔍 Analysis operations
   - ✅ Successful completions
   - ❌ Errors and failures

4. **Summary Statistics**: Final overview of the process
   ```javascript
   console.log(colors.cyan('\n📊 Summary:'));
   console.log(colors.cyan(`Total products processed: ${processedCount}`));
   console.log(colors.cyan(`Similarity threshold used: ${similarityThreshold}`));
   console.log(colors.cyan(`Batch size used: ${BATCH_SIZE}`));
   ```

## Database Schema

### Product Schema
```javascript
const productSchema = new mongoose.Schema({
  _id: mongoose.Schema.Types.ObjectId,
  name: String,
  description: String,
  // ... other product fields
});
```

### Similarity Schema
```javascript
const similaritySchema = new mongoose.Schema({
  productId: mongoose.Schema.Types.ObjectId,
  similarProducts: [{
    similarProductId: mongoose.Schema.Types.ObjectId,
    similarityScore: Number
  }]
});
```

## Installation

1. Clone the repository
   ```bash
   git clone https://github.com/yourusername/natural.git
   cd natural
   ```

2. Install dependencies
   ```bash
   npm install
   ```

3. Create a `.env` file with your MongoDB credentials
   ```
   # MongoDB Connection
   MONGODB_USERNAME=your_username
   MONGODB_PASSWORD=your_password
   MONGODB_CLUSTER=your_cluster.mongodb.net
   MONGODB_DATABASE=your_database_name
   
   # Application Settings
   SIMILARITY_THRESHOLD=0.1
   BATCH_SIZE=100
   ```

## Usage

Run the application:
```bash
node calculateSimilarities.js
```

## Performance Considerations

- **Memory Usage**: Pre-calculating vectors reduces redundant calculations but increases memory usage
- **Batch Processing**: Processing products in batches balances memory usage with performance
- **Bulk Database Operations**: Using MongoDB's bulk operations reduces database overhead
- **Similarity Threshold**: Adjusting the threshold controls the number of similar products stored

## Future Enhancements

- Word embeddings (Word2Vec, GloVe) for more semantic similarity
- Category-based similarity weighting
- Price and other metadata factoring into similarity scores
- API endpoints for retrieving similar products
- Scheduled recalculation of similarities for new products

## License

MIT

## Contributors

- Your Name - Initial work
