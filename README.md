# MENG E-Commerce API

[![Node.js](https://img.shields.io/badge/Node.js-v18.x-green.svg)](https://nodejs.org/)
[![Express](https://img.shields.io/badge/Express-v4.21.2-blue.svg)](https://expressjs.com/)
[![MongoDB](https://img.shields.io/badge/MongoDB-v5.9.2-green.svg)](https://www.mongodb.com/)
[![License](https://img.shields.io/badge/License-ISC-blue.svg)](LICENSE)

A comprehensive e-commerce RESTful API built with Express.js and MongoDB, featuring advanced AI-powered search, product similarity recommendations, multiple payment methods, social authentication, real-time notifications, and a complete e-commerce ecosystem.

## 🚀 Key Features

- **🤖 AI-Powered Search**: Natural language product search using Google Gemini AI
- **🔍 Product Similarity Engine**: TF-IDF and cosine similarity-based recommendations
- **💳 Multiple Payment Methods**: Stripe integration and cash on delivery
- **🔐 Advanced Authentication**: JWT, OAuth (Google/Facebook), phone OTP verification
- **📱 SMS Integration**: Twilio-powered SMS notifications and OTP
- **📧 Email System**: Comprehensive email notifications with Nodemailer and Brevo
- **☁️ Cloud Storage**: Cloudinary integration for image management
- **🛒 Complete E-commerce**: Cart, wishlist, orders, coupons, reviews
- **📍 Address Management**: Multiple shipping addresses per user
- **👥 Role-Based Access**: Admin, manager, and user roles
- **📊 Advanced Filtering**: Search, pagination, sorting, and field limiting
- **🔄 Real-time Updates**: Session management with MongoDB store
- **📞 Contact System**: Contact us functionality with email integration
- **🔒 Security Features**: Password hashing, JWT tokens, session management
- **🖼️ Image Processing**: Sharp-powered image optimization and resizing

## 🛠 Technologies Used

### Core Framework
- **Express.js**: Fast, unopinionated web framework for Node.js
- **Node.js**: JavaScript runtime environment
- **MongoDB/Mongoose**: NoSQL database and elegant MongoDB object modeling

### Authentication & Security
- **JWT (jsonwebtoken)**: JSON Web Tokens for secure authentication
- **Bcrypt.js**: Password hashing and security
- **Passport.js**: Authentication middleware with OAuth strategies
- **Express Session**: Session management with MongoDB store
- **Express Validator**: Comprehensive request validation middleware
- **Connect-Mongo**: MongoDB session store for Express sessions

### AI & Machine Learning
- **Google Generative AI**: Gemini API integration for AI-powered search
- **Natural**: Natural Language Processing library for text processing and similarity calculations
- **TF-IDF**: Term Frequency-Inverse Document Frequency for content analysis

### Payment & Communication
- **Stripe**: Complete payment processing solution with webhooks
- **Twilio**: SMS messaging and phone verification
- **Nodemailer**: Email sending capabilities with SMTP support

### File Handling & Media
- **Multer**: Middleware for handling multipart/form-data and file uploads
- **Sharp**: High-performance image processing and optimization
- **Cloudinary**: Cloud-based image and video management
- **UUID**: Unique identifier generation

### Development & Utilities
- **Morgan**: HTTP request logger middleware
- **CORS**: Cross-Origin Resource Sharing
- **Compression**: Response compression middleware
- **Slugify**: URL-friendly slug generation
- **Colors**: Terminal string styling
- **CLI Progress**: Terminal progress bars for batch operations
- **Cookie Parser**: Cookie parsing middleware
- **Path-to-RegExp**: Express route path matching

## 🚀 Installation & Setup

### Prerequisites
- Node.js (v18.x or higher)
- MongoDB (local or cloud instance)
- npm or yarn package manager

### 1. Clone the Repository
```bash
git clone https://github.com/yourusername/meng-ecommerce-api.git
cd meng-ecommerce-api
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Configuration
Create a `config.env` file in the root directory with all the required environment variables (see Environment Variables section below).

### 4. Start the Application

#### Development Mode
```bash
npm run start:dev
```

#### Production Mode
```bash
npm run start:prod
```

The server will start on `http://localhost:8080` (or your configured PORT).

### 5. Run Product Similarity Calculator (Optional)
To enable product recommendations, run the similarity calculator:
```bash
node similarity-calculator.js
```

## API Endpoints

### Category Endpoints
- `GET /api/v1/categories` - Get all categories
- `POST /api/v1/categories` - Create a new category
- `GET /api/v1/categories/:id` - Get a specific category
- `PUT /api/v1/categories/:id` - Update a specific category
- `DELETE /api/v1/categories/:id` - Delete a specific category

### Subcategory Endpoints
- `GET /api/v1/subcategories` - Get all subcategories
- `POST /api/v1/subcategories` - Create a new subcategory
- `GET /api/v1/subcategories/:id` - Get a specific subcategory
- `PUT /api/v1/subcategories/:id` - Update a specific subcategory
- `DELETE /api/v1/subcategories/:id` - Delete a specific subcategory
- `GET /api/v1/categories/:categoryId/subcategory` - Get subcategories for a specific category

### Brand Endpoints
- `GET /api/v1/brand` - Get all brands
- `POST /api/v1/brand` - Create a new brand
- `GET /api/v1/brand/:id` - Get a specific brand
- `PUT /api/v1/brand/:id` - Update a specific brand
- `DELETE /api/v1/brand/:id` - Delete a specific brand

### Product Endpoints
- `GET /api/v1/products` - Get all products
- `POST /api/v1/products` - Create a new product
- `GET /api/v1/products/:id` - Get a specific product
- `PUT /api/v1/products/:id` - Update a specific product
- `DELETE /api/v1/products/:id` - Delete a specific product
- `GET /api/v1/products/:productId/similar` - Get similar products based on content similarity

### Review Endpoints
- `GET /api/v1/reviews` - Get all reviews
- `POST /api/v1/reviews` - Create a new review
- `GET /api/v1/reviews/:id` - Get a specific review
- `PUT /api/v1/reviews/:id` - Update a specific review
- `DELETE /api/v1/reviews/:id` - Delete a specific review

### Authentication Endpoints
- `POST /api/v1/auth/signup` - Register a new user
- `POST /api/v1/auth/login` - Login a user
- `GET /api/v1/auth/logout` - Logout a user
- `POST /api/v1/auth/forgotPassword` - Request password reset
- `POST /api/v1/auth/verifyResetCode` - Verify password reset code
- `PUT /api/v1/auth/resetPassword` - Reset password with code

### OAuth Endpoints
- `GET /api/v1/auth/google` - Authenticate with Google
- `GET /api/v1/auth/google/callback` - Google OAuth callback
- `GET /api/v1/auth/facebook` - Authenticate with Facebook
- `GET /api/v1/auth/facebook/callback` - Facebook OAuth callback

### User Endpoints
- `GET /api/v1/users/me` - Get current user profile
- `PUT /api/v1/users/updateMe` - Update current user profile
- `PUT /api/v1/users/changeMyPassword` - Change current user password
- `DELETE /api/v1/users/deleteMe` - Delete current user account

### Wishlist Endpoints
- `GET /api/v1/wishlist` - Get user's wishlist
- `POST /api/v1/wishlist` - Add product to wishlist
- `DELETE /api/v1/wishlist/:productId` - Remove product from wishlist

### Address Endpoints
- `GET /api/v1/address` - Get user's addresses
- `POST /api/v1/address` - Add a new address
- `PUT /api/v1/address/:addressId` - Update an address
- `DELETE /api/v1/address/:addressId` - Delete an address

### Phone OTP Endpoints
- `POST /api/auth/phone/send-otp` - Send OTP to phone
- `POST /api/auth/phone/verify-otp` - Verify phone OTP

### Coupon Endpoints
- `GET /api/v1/coupon` - Get all coupons
- `POST /api/v1/coupon` - Create a new coupon
- `GET /api/v1/coupon/:id` - Get a specific coupon
- `PUT /api/v1/coupon/:id` - Update a specific coupon
- `DELETE /api/v1/coupon/:id` - Delete a specific coupon

### Cart Endpoints
- `GET /api/v1/cart` - Get user's cart
- `POST /api/v1/cart` - Add product to cart
- `PUT /api/v1/cart/:itemId` - Update cart item
- `DELETE /api/v1/cart/:itemId` - Remove item from cart
- `DELETE /api/v1/cart` - Clear cart

### Order Endpoints
- `GET /api/v1/orders` - Get user's orders
- `POST /api/v1/orders` - Create a new order
- `GET /api/v1/orders/:id` - Get a specific order
- `PUT /api/v1/orders/:id/pay` - Update order to paid
- `PUT /api/v1/orders/:id/deliver` - Update order to delivered

### AI-Powered Search Endpoint
- `GET /api/v1/llm-search?q=your search query` - Search products using AI language understanding

### Contact Us Endpoints
- `POST /api/v1/contactUs` - Send contact form email (requires authentication)

## 🔧 Environment Variables

Create a `config.env` file in the root directory with the following variables:

```env
# Server Configuration
NODE_ENV=development
PORT=8080
HOST=0.0.0.0

# Database Configuration
db_URI=mongodb+srv://username:<EMAIL>/database_name
db_Username=your_mongodb_username
db_Password=your_mongodb_password
db_NAME=your_database_name

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key
JWT_EXPIRES_IN=90d

# Session Configuration
SESSION_SECRET=your_session_secret_key

# Email Configuration (Brevo/Sendinblue)
EMAIL_HOST=smtp-relay.brevo.com
EMAIL_PORT=587
EMAIL_USERNAME=your_brevo_smtp_username
EMAIL_PASSWORD=your_brevo_smtp_password
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=Your App Name
EMAIL_SECURE=false

# Alternative Email Configuration (Gmail)
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
ADMIN_EMAIL=<EMAIL>

# OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret

# Payment Configuration (Stripe)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Cloud Storage (Cloudinary)
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# AI Configuration
GEMINI_API_KEY=your_google_gemini_api_key

# Product Similarity Configuration
SIMILARITY_THRESHOLD=0.1
BATCH_SIZE=100

# Company Information
COMPANY_NAME=your_company_name
```

## 📁 Project Structure

```
├── config/                     # Configuration files
│   └── database.js            # MongoDB connection setup
├── middleware/                 # Custom middleware functions
│   ├── errorMiddleware.js     # Global error handling
│   ├── uploadImageMiddleware.js # Image upload handling
│   └── validatorMiddleware.js # Request validation
├── models/                     # Mongoose data models
│   ├── brandModel.js          # Brand schema
│   ├── cartModel.js           # Shopping cart schema
│   ├── categoryModel.js       # Product category schema
│   ├── contactUsModel.js      # Contact form schema
│   ├── couponModel.js         # Discount coupon schema
│   ├── orderModel.js          # Order management schema
│   ├── productModel.js        # Product schema
│   ├── productSimilarityModel.js # Product similarity data
│   ├── reviewModel.js         # Product review schema
│   ├── subCategoryModel.js    # Subcategory schema
│   └── userModel.js           # User account schema
├── routes/                     # API route definitions
│   ├── addressRoute.js        # Address management
│   ├── authRoute.js           # Authentication routes
│   ├── brandRoute.js          # Brand management
│   ├── cartRoute.js           # Shopping cart operations
│   ├── categoryRoute.js       # Category management
│   ├── contactUsRoute.js      # Contact form handling
│   ├── couponRoute.js         # Coupon management
│   ├── llmSearchRoute.js      # AI-powered search
│   ├── orderRoute.js          # Order processing
│   ├── phoneOtpRoute.js       # Phone verification
│   ├── productRoute.js        # Product management
│   ├── reviewRoute.js         # Review system
│   ├── subCategoryRoute.js    # Subcategory management
│   ├── userRoute.js           # User management
│   ├── wishlistRoute.js       # Wishlist functionality
│   └── index.js               # Route mounting
├── services/                   # Business logic layer
│   ├── auth/                  # Authentication services
│   │   ├── loginService.js    # User login logic
│   │   ├── logoutService.js   # User logout logic
│   │   ├── passwordService.js # Password management
│   │   ├── protectService.js  # Route protection
│   │   ├── signupService.js   # User registration
│   │   ├── socialAuthService.js # OAuth integration
│   │   ├── tokenService.js    # JWT token management
│   │   └── index.js           # Auth services export
│   ├── addressService.js      # Address management logic
│   ├── brandService.js        # Brand operations
│   ├── cartService.js         # Cart functionality
│   ├── categoryService.js     # Category operations
│   ├── contactUsService.js    # Contact form processing
│   ├── couponService.js       # Coupon logic
│   ├── handlersFactory.js     # Generic CRUD operations
│   ├── llmSearchService.js    # AI search implementation
│   ├── orderService.js        # Order processing & Stripe integration
│   ├── productService.js      # Product management
│   ├── reviewService.js       # Review system
│   ├── subCategoryService.js  # Subcategory operations
│   ├── userService.js         # User management
│   └── wishlistService.js     # Wishlist operations
├── utils/                      # Utility functions
│   ├── validators/            # Request validation schemas
│   ├── apiError.js           # Custom error handling
│   ├── apiFeatures.js        # Query building and pagination
│   ├── sendEmail.js          # Email utility
│   └── sendSms.js            # SMS utility
├── test/                       # Test files
│   └── smsTest.js            # SMS functionality tests
├── similarity-calculator.js    # Product similarity calculation script
├── server.js                  # Application entry point
├── package.json               # Project dependencies and scripts
└── config.env                 # Environment variables
```

## ✨ Detailed Features

### 🔐 Authentication & Security
- **JWT Authentication**: Secure token-based authentication with configurable expiration
- **Social Login**: OAuth integration with Google and Facebook using Passport.js
- **Phone Verification**: SMS-based OTP verification using Twilio
- **Password Management**: Secure password reset flow with email verification
- **Role-Based Access Control**: Admin, manager, and user roles with different permissions
- **Session Management**: MongoDB-backed session storage with configurable TTL
- **Password Security**: Bcrypt hashing with salt rounds for password protection

### 🛒 E-Commerce Core Features
- **Product Management**: Complete CRUD operations with image upload and processing
- **Category & Subcategory System**: Hierarchical product organization
- **Brand Management**: Brand-based product filtering and organization
- **Shopping Cart**: Persistent cart with size selection and quantity management
- **Wishlist**: Save products for later with user-specific lists
- **Order Management**: Complete order lifecycle from creation to delivery
- **Review System**: Product reviews with ratings and user feedback
- **Coupon System**: Discount codes with various types and restrictions

### 💳 Payment & Billing
- **Stripe Integration**: Complete payment processing with webhooks
- **Multiple Payment Methods**: Credit cards and cash on delivery
- **Order Tracking**: Real-time order status updates
- **Tax Calculation**: Configurable tax rates and shipping costs
- **Webhook Handling**: Secure Stripe webhook processing for payment confirmations

### 🤖 AI & Machine Learning
- **Natural Language Search**: Google Gemini AI-powered product search
- **Product Similarity**: TF-IDF and cosine similarity-based recommendations
- **Semantic Understanding**: Multi-language query interpretation
- **Fallback Mechanisms**: Graceful degradation to keyword search when AI fails
- **Batch Processing**: Efficient similarity calculation for large product catalogs

### 📱 Communication & Notifications
- **Email System**: Comprehensive email notifications using Nodemailer
- **SMS Integration**: Twilio-powered SMS for OTP and notifications
- **Contact Form**: Customer inquiry system with email forwarding
- **Multiple Email Providers**: Support for Gmail, Brevo, and custom SMTP

### 🖼️ Media & File Management
- **Image Upload**: Multer-based file upload with validation
- **Image Processing**: Sharp-powered image optimization and resizing
- **Cloud Storage**: Cloudinary integration for scalable media management
- **Multiple Image Support**: Product galleries with cover and additional images

### 📊 Advanced Features
- **Search & Filtering**: Advanced product search with multiple criteria
- **Pagination**: Efficient data pagination for large datasets
- **Sorting**: Multiple sorting options for product listings
- **Field Limiting**: API response optimization with selective field returns
- **Query Building**: Dynamic MongoDB query construction
- **Error Handling**: Comprehensive error management with custom error classes
- **Logging**: Request logging with Morgan for development and production
- **Compression**: Response compression for improved performance
- **CORS**: Cross-origin resource sharing configuration

## Product Similarity Engine

The project includes a product recommendation system based on content similarity:

- **TF-IDF Vectorization**: Converts product text (name, description) into numerical vectors
- **Cosine Similarity**: Measures similarity between product vectors
- **Batch Processing**: Efficiently processes large product catalogs
- **Pre-computation**: Calculates similarities offline and stores results for fast retrieval
- **Progress Visualization**: Visual feedback during similarity calculation

To run the similarity calculation:

```bash
# Run the similarity calculator
node similarity-calculator.js
```

This will:
1. Connect to your MongoDB database
2. Fetch all products
3. Calculate similarity scores between products using TF-IDF and cosine similarity
4. Store the results in the ProductSimilarity collection
5. Display progress with visual indicators

## 💳 Payment System Integration

### Stripe Payment Processing
The application includes comprehensive Stripe integration for secure payment processing:

#### Features
- **Checkout Sessions**: Secure hosted checkout pages
- **Webhook Handling**: Real-time payment status updates
- **Multiple Currencies**: Support for different currencies (default: EGP)
- **Order Tracking**: Automatic order creation upon successful payment
- **Refund Support**: Built-in refund capabilities

#### Webhook Endpoint
```
POST /webhook-stripe
```

This endpoint handles Stripe webhooks for:
- Payment confirmations
- Order status updates
- Automatic order creation
- Cart clearing after successful payment

#### Payment Flow
1. User adds items to cart
2. Proceeds to checkout
3. Stripe checkout session created
4. User completes payment on Stripe
5. Webhook confirms payment
6. Order automatically created
7. Cart cleared
8. User redirected to success page

### Cash on Delivery
Alternative payment method for users who prefer to pay upon delivery:
- No online payment required
- Order created immediately
- Payment status marked as pending
- Manual confirmation by admin upon delivery

## AI-Powered Search Implementation

The project includes an advanced search feature powered by Google's Gemini AI:

### Key Features

- **Natural Language Understanding**: Interprets user queries in any language
- **Semantic Search**: Understands the meaning behind search terms, not just keywords
- **Multi-parameter Extraction**: Identifies product attributes, categories, price ranges, and more
- **Fallback Mechanism**: Gracefully degrades to keyword search if AI processing fails

### Implementation Details

- **Gemini API Integration**: Uses Google's Generative AI for query understanding
- **Prompt Engineering**: Carefully crafted prompts to extract structured data from natural language
- **MongoDB Query Construction**: Dynamically builds database queries based on AI interpretation
- **Error Handling**: Robust error handling with graceful fallback to basic search

### Usage

```
GET /api/v1/llm-search?q=your search query
```

Example queries:
- "red gaming laptop under $1500 with RTX graphics"
- "summer dresses for women"
- "safety shoes size 42 for less than 500 pounds"

### Response Format

```json
{
  "originalQuery": "user's original query",
  "searchMethod": "AI-Enhanced Search",
  "llmInterpretation": {
    "keywords": ["extracted", "keywords"],
    "categories": ["identified", "categories"],
    "minPrice": 100,
    "maxPrice": 500
  },
  "mongoQueryUsed": {
    "$or": [
      { "name": { "$in": [/keyword1/i, /keyword2/i] } },
      { "description": { "$in": [/keyword1/i, /keyword2/i] } }
    ],
    "price": { "$gte": 100, "$lte": 500 }
  },
  "resultsCount": 5,
  "results": [
    // Array of product objects
  ]
}
```

## Key Notes and Observations for the LLM Search Implementation

### Core Goal
The primary objective is to use the Gemini Large Language Model (LLM) to interpret a user's natural language search query (in any language) for an e-commerce context. The LLM's interpretation is then used to construct a MongoDB query to find relevant products. A basic keyword-based search serves as a fallback if the AI processing fails.

### Gemini API Integration
- **Initialization**: The GoogleGenerativeAI client and the specific model are initialized once when the module loads, provided the GEMINI_API_KEY is present in the environment variables.
- **API Key Check**: A crucial check for GEMINI_API_KEY at startup prevents the application from running with a non-functional AI search if the key is missing.
- **Model Name Flexibility**: The code tries multiple model names to find one that works with the current API version.
- **Prompt Engineering**: The prompt is designed to instruct the LLM to act as an e-commerce query parser and demand a strict JSON-only output.

### LLM Output Processing
- **JSON Cleaning**: Logic is dedicated to cleaning the LLM's raw text output to isolate a valid JSON string.
- **JSON Parsing**: JSON.parse() is used on the cleaned text. If this fails, the system gracefully falls back to the basic search.

### MongoDB Query Construction
- **Dynamic Query Building**: The mongoQuery object is built dynamically based on the parameters extracted from the LLM's JSON output.
- **Keyword Search**: Uses $or with case-insensitive regular expressions for keywords across name, description, and tags.
- **Categorical Filters**: Uses $in with RegExp for categories and brand.
- **Price Range**: Uses $gte for minPrice and $lte for maxPrice.

### Fallback Search Mechanism
- **Robustness**: If any part of the AI processing pipeline fails, the system falls back to basic search.
- **Basic Keyword Extraction**: The fallback splits the user query into words and uses them for a regex search.
- **Simple Price Extraction**: The fallback attempts to extract numbers from the query for price filtering.
- **Clear Indication**: The response JSON clearly indicates when the fallback search was used and the reason.

## 👥 User Management & Roles

### User Roles
The system supports three distinct user roles with different permissions:

#### 1. User (Default)
- Browse products and categories
- Manage personal profile and addresses
- Add items to cart and wishlist
- Place orders and write reviews
- View order history
- Contact support

#### 2. Manager
- All user permissions
- Manage products (create, update, delete)
- Manage categories and brands
- View all orders
- Manage coupons
- Access to analytics

#### 3. Admin
- All manager permissions
- Manage users and roles
- Access to all system features
- Manage system settings
- Full database access

### User Profile Features
- **Personal Information**: Name, email, phone, address
- **Profile Image**: Cloudinary-hosted profile pictures
- **Address Management**: Multiple shipping addresses
- **Order History**: Complete order tracking
- **Wishlist**: Saved products for later
- **Account Security**: Password change and account deletion
- **Social Login**: Google and Facebook integration

### Authentication Methods
1. **Email/Password**: Traditional registration and login
2. **Google OAuth**: One-click Google account integration
3. **Facebook OAuth**: Facebook account integration
4. **Phone Verification**: SMS-based OTP verification

### Potential Areas for Further Improvement
- **LLM Output Validation**: Use a schema validation library for more robust parsing.
- **More Sophisticated Fallback**: Implement more advanced NLP techniques for the fallback search.
- **$text Search**: Consider creating a $text index for more efficient keyword searching.
- **Advanced Prompting**: Explore more advanced prompting techniques for complex queries.
- **Performance Monitoring**: Monitor latency introduced by the LLM call.
- **Cost Management**: Implement logging and monitoring for API usage.
- **User Feedback Loop**: Collect feedback on search results to fine-tune prompts over time.

## 🧪 Testing

### Available Tests
The project includes test files for various functionalities:

```bash
# Run SMS functionality tests
node test/smsTest.js
```

### Testing Features
- **SMS Testing**: Verify Twilio integration and OTP functionality
- **API Endpoints**: Test all REST endpoints
- **Authentication**: Verify JWT and OAuth flows
- **Payment Processing**: Test Stripe integration
- **File Upload**: Verify image upload and processing

### Recommended Testing Approach
1. **Unit Tests**: Test individual service functions
2. **Integration Tests**: Test API endpoints with database
3. **End-to-End Tests**: Test complete user workflows
4. **Load Testing**: Test performance under load
5. **Security Testing**: Verify authentication and authorization

## 🚀 Deployment

### Environment Setup
1. **Production Environment Variables**: Update `config.env` with production values
2. **Database**: Use MongoDB Atlas or dedicated MongoDB instance
3. **File Storage**: Configure Cloudinary for production
4. **Email Service**: Set up production email service (Brevo/Gmail)
5. **SMS Service**: Configure Twilio for production

### Deployment Platforms
The application is configured to work with:
- **Render**: Cloud platform deployment
- **Heroku**: Container-based deployment
- **AWS**: EC2 or Elastic Beanstalk
- **DigitalOcean**: Droplet deployment
- **Vercel**: Serverless deployment

### Production Considerations
- **Environment**: Set `NODE_ENV=production`
- **Security**: Use HTTPS in production
- **Monitoring**: Implement logging and monitoring
- **Backup**: Regular database backups
- **Scaling**: Consider load balancing for high traffic

## 🔧 Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check MongoDB connection string
# Ensure IP whitelist includes your IP
# Verify username and password
```

#### Environment Variables
```bash
# Ensure all required variables are set in config.env
# Check for typos in variable names
# Verify API keys are valid
```

#### Image Upload Issues
```bash
# Check Cloudinary configuration
# Verify file size limits
# Ensure proper file types
```

#### Payment Issues
```bash
# Verify Stripe keys (test vs production)
# Check webhook endpoint configuration
# Ensure proper HTTPS for webhooks
```

#### AI Search Not Working
```bash
# Verify GEMINI_API_KEY is set
# Check API quota and billing
# Review error logs for specific issues
```

### Debug Mode
Enable debug logging by setting:
```env
NODE_ENV=development
```

## 🤝 Contributing

We welcome contributions to improve the MENG E-Commerce API! Here's how you can help:

### Getting Started
1. Fork the repository
2. Clone your fork: `git clone https://github.com/yourusername/meng-ecommerce-api.git`
3. Create a feature branch: `git checkout -b feature/amazing-feature`
4. Install dependencies: `npm install`
5. Set up your development environment with `config.env`

### Development Guidelines
- Follow existing code style and conventions
- Write clear, descriptive commit messages
- Add tests for new features
- Update documentation as needed
- Ensure all tests pass before submitting

### Submitting Changes
1. Commit your changes: `git commit -m 'Add some amazing feature'`
2. Push to your branch: `git push origin feature/amazing-feature`
3. Open a Pull Request with a clear description of changes
4. Wait for code review and address any feedback

### Areas for Contribution
- Additional payment gateways
- Enhanced AI search capabilities
- Mobile app integration
- Performance optimizations
- Additional language support
- Security enhancements

## 📄 License

This project is licensed under the ISC License. See the LICENSE file for details.

## 🙏 Acknowledgments

- **Google Gemini AI**: For providing advanced natural language processing capabilities
- **Stripe**: For secure and reliable payment processing
- **Cloudinary**: For efficient image management and optimization
- **Twilio**: For SMS and communication services
- **MongoDB**: For flexible and scalable database solutions
- **Express.js Community**: For the robust web framework
- **Open Source Contributors**: For the amazing libraries and tools used in this project

## 📞 Support

If you encounter any issues or have questions:

1. **Check the Documentation**: Review this README and inline code comments
2. **Search Issues**: Look through existing GitHub issues
3. **Create an Issue**: Open a new issue with detailed information
4. **Contact**: Reach out through the contact form in the application

## 🔗 Related Projects

- **Frontend Application**: [Link to frontend repository if available]
- **Mobile Application**: [Link to mobile app repository if available]
- **Admin Dashboard**: [Link to admin panel repository if available]

## 📈 Project Status

This project is actively maintained and under continuous development. Current version supports:
- ✅ Complete e-commerce functionality
- ✅ AI-powered search
- ✅ Payment processing
- ✅ User management
- ✅ Product recommendations
- 🔄 Mobile app integration (in progress)
- 🔄 Advanced analytics (planned)
- 🔄 Multi-language support (planned)

---

**Built with ❤️ for modern e-commerce solutions**


